#!/usr/bin/env python3
"""
Test team statistics endpoint
"""
import requests
import json

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def test_team_statistics():
    """Test getting team statistics"""
    print("=== Testing Team Statistics ===")

    # First get a fixture with participants
    fixture_data = make_api_request('fixtures/463', {'include': 'participants'})

    if not fixture_data or 'data' not in fixture_data:
        print("✗ Could not get fixture")
        return

    fixture = fixture_data['data']
    if 'participants' not in fixture or len(fixture['participants']) < 2:
        print("✗ No participants found")
        return

    team_id = fixture['participants'][0]['id']
    team_name = fixture['participants'][0]['name']

    print(f"Testing team: {team_name} (ID: {team_id})")

    # Test different team endpoints
    team_endpoints = [
        f'teams/{team_id}',
        f'teams/{team_id}/fixtures',
        f'teams/{team_id}/squad',
        f'teams/{team_id}/statistics'
    ]

    for endpoint in team_endpoints:
        print(f"\nTesting endpoint: {endpoint}")
        data = make_api_request(endpoint, {'per_page': 2})
        if data:
            print(f"  ✓ Endpoint works")
            if 'data' in data:
                if isinstance(data['data'], list):
                    print(f"    Found {len(data['data'])} items")
                    if len(data['data']) > 0:
                        print(f"    Sample keys: {list(data['data'][0].keys())}")
                else:
                    print(f"    Single item keys: {list(data['data'].keys())}")
        else:
            print(f"  ✗ Endpoint failed")

    # Test fixture statistics directly
    print(f"\n=== Testing Fixture Statistics ===")
    fixture_with_stats = make_api_request('fixtures/463', {'include': 'statistics'})

    if fixture_with_stats and 'data' in fixture_with_stats:
        fixture = fixture_with_stats['data']
        if 'statistics' in fixture and fixture['statistics']:
            print(f"✓ Found {len(fixture['statistics'])} statistics entries")

            # Show sample statistics
            for i, stat in enumerate(fixture['statistics'][:3]):
                print(f"\nStat {i+1}:")
                print(f"  Keys: {list(stat.keys())}")
                # Show all fields to understand structure
                for key, value in stat.items():
                    print(f"    {key}: {value}")
        else:
            print("✗ No statistics in fixture")
    else:
        print("✗ Could not get fixture with statistics")

def main():
    """Run team statistics test"""
    print("Testing Team Statistics API")
    print("=" * 40)
    
    test_team_statistics()
    
    print("\n" + "=" * 40)
    print("Team statistics testing complete!")

if __name__ == "__main__":
    main()
