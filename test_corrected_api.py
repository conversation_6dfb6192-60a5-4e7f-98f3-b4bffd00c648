#!/usr/bin/env python3
"""
Test the corrected API integration
"""
import requests
import json
from datetime import datetime, timedelta

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def test_corrected_fixtures():
    """Test the corrected fixtures endpoint"""
    print("=== Testing Corrected Fixtures Endpoint ===")

    # First try without includes
    print("Testing basic fixtures...")
    data = make_api_request('fixtures', {'per_page': 3})

    if not data or 'data' not in data:
        print("✗ Basic fixtures failed")
        return None

    print(f"✓ Found {len(data['data'])} basic fixtures")
    fixture_id = data['data'][0]['id']

    # Test individual includes
    includes_to_test = ['league', 'participants', 'scores', 'statistics']
    working_includes = []

    for include in includes_to_test:
        print(f"\nTesting include: {include}")
        test_data = make_api_request(f'fixtures/{fixture_id}', {'include': include})
        if test_data and 'data' in test_data:
            fixture = test_data['data']
            if include in fixture:
                print(f"  ✓ {include} works - type: {type(fixture[include])}")
                working_includes.append(include)

                # Show sample data
                if include == 'participants' and isinstance(fixture[include], list):
                    print(f"    Participants: {len(fixture[include])} teams")
                    for i, participant in enumerate(fixture[include][:2]):
                        print(f"      Team {i+1}: {participant.get('name', 'Unknown')} (ID: {participant.get('id')})")
                elif include == 'league' and isinstance(fixture[include], dict):
                    print(f"    League: {fixture[include].get('name', 'Unknown')}")
            else:
                print(f"  ✗ {include} not in response")
        else:
            print(f"  ✗ {include} failed")

    print(f"\nWorking includes: {working_includes}")

    # Try with working includes
    if working_includes:
        combined_includes = ','.join(working_includes)
        print(f"\nTesting combined includes: {combined_includes}")
        combined_data = make_api_request(f'fixtures/{fixture_id}', {'include': combined_includes})
        if combined_data and 'data' in combined_data:
            print("✓ Combined includes work")
            return fixture_id

    return fixture_id

def test_match_details(match_id):
    """Test getting match details with the corrected structure"""
    print(f"\n=== Testing Match Details (ID: {match_id}) ===")
    
    data = make_api_request(f'fixtures/{match_id}', {
        'include': 'league,participants'
    })
    
    if data and 'data' in data:
        match = data['data']
        print("✓ Match details retrieved successfully")
        print(f"  League: {match.get('league', {}).get('name', 'Unknown')}")
        
        if 'participants' in match and len(match['participants']) >= 2:
            print(f"  Team 1: {match['participants'][0].get('name', 'Unknown')} (ID: {match['participants'][0]['id']})")
            print(f"  Team 2: {match['participants'][1].get('name', 'Unknown')} (ID: {match['participants'][1]['id']})")
            return True
        else:
            print("  ✗ Participants not found")
            return False
    else:
        print("✗ Failed to retrieve match details")
        return False

def main():
    """Run corrected API tests"""
    print("Testing Corrected SportsMonk API Integration")
    print("=" * 50)
    
    # Test corrected fixtures
    match_id = test_corrected_fixtures()
    
    if match_id:
        # Test match details
        test_match_details(match_id)
    
    print("\n" + "=" * 50)
    print("Corrected API testing complete!")

if __name__ == "__main__":
    main()
