#!/usr/bin/env python3
"""
Test Flask application endpoints
"""
import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_endpoint(endpoint, description):
    """Test a Flask endpoint"""
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=30)
        if response.status_code == 200:
            print(f"✅ {description}: SUCCESS")
            if endpoint.startswith('/api/'):
                data = response.json()
                if 'data' in data:
                    print(f"   Found {len(data['data'])} items")
                elif 'error' in data:
                    print(f"   API Error: {data['error']}")
                else:
                    print(f"   Response keys: {list(data.keys())}")
            return True
        else:
            print(f"❌ {description}: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {description}: {e}")
        return False

def main():
    """Test Flask application endpoints"""
    print("Testing Flask Application Endpoints")
    print("=" * 50)
    
    # Test main page
    test_endpoint("/", "Main page")
    
    # Test API endpoints
    test_endpoint("/api/test", "API test endpoint")
    test_endpoint("/api/upcoming-matches", "Upcoming matches API")
    
    # Test match details page (using a fixture ID we know exists)
    test_endpoint("/match/463", "Match details page")
    
    # Test match players API
    test_endpoint("/api/match/463/players", "Match players API (default)")
    test_endpoint("/api/match/463/players?stat=fouls_committed&matches=10", "Match players API (with params)")
    
    print("\n" + "=" * 50)
    print("Flask endpoint testing complete!")
    print("\nIf all tests pass, the application is working correctly.")
    print("You can now open http://127.0.0.1:5000 in your browser to use the interface.")

if __name__ == "__main__":
    main()
