#!/usr/bin/env python3
"""
Test detailed structure of available data
"""
import requests
import json

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def examine_statistics_structure():
    """Examine the structure of statistics data"""
    print("=== Examining Statistics Structure ===")
    
    # Use fixture 468 which had statistics
    fixture_data = make_api_request('fixtures/468', {'include': 'statistics'})
    
    if fixture_data and 'data' in fixture_data:
        fixture = fixture_data['data']
        if 'statistics' in fixture and fixture['statistics']:
            print(f"Found {len(fixture['statistics'])} statistics entries")
            
            for i, stat in enumerate(fixture['statistics']):
                print(f"\nStatistic {i+1}:")
                for key, value in stat.items():
                    print(f"  {key}: {value}")
                    if key == 'data' and isinstance(value, dict):
                        print(f"    Data contents:")
                        for data_key, data_value in value.items():
                            print(f"      {data_key}: {data_value}")

def examine_lineups_structure():
    """Examine the structure of lineups data"""
    print("\n=== Examining Lineups Structure ===")
    
    # Use fixture 463 which had lineups
    fixture_data = make_api_request('fixtures/463', {'include': 'lineups'})
    
    if fixture_data and 'data' in fixture_data:
        fixture = fixture_data['data']
        if 'lineups' in fixture and fixture['lineups']:
            print(f"Found {len(fixture['lineups'])} lineup entries")
            
            for i, lineup in enumerate(fixture['lineups'][:5]):  # Show first 5
                print(f"\nLineup {i+1}:")
                for key, value in lineup.items():
                    if key == 'player' and isinstance(value, dict):
                        print(f"  player: {value}")
                    else:
                        print(f"  {key}: {value}")

def examine_players_endpoint():
    """Examine the players endpoint structure"""
    print("\n=== Examining Players Endpoint ===")
    
    players_data = make_api_request('players', {'per_page': 2})
    
    if players_data and 'data' in players_data:
        print(f"Found {len(players_data['data'])} players")
        
        for i, player in enumerate(players_data['data']):
            print(f"\nPlayer {i+1}:")
            for key, value in player.items():
                print(f"  {key}: {value}")

def main():
    """Examine detailed structure"""
    print("Examining Detailed API Structure")
    print("=" * 40)
    
    examine_statistics_structure()
    examine_lineups_structure()
    examine_players_endpoint()
    
    print("\n" + "=" * 40)
    print("Structure examination complete!")

if __name__ == "__main__":
    main()
