{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 id="matchTitle">Match Details</h1>
            <a href="/" class="btn btn-secondary">Back to Matches</a>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <select id="statType" class="form-select">
                    <option value="fouls_committed">Fouls Committed (Yellow Cards)</option>
                    <option value="goals_scored">Goals Scored</option>
                    <option value="cards_received">Cards Received</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="numMatches" class="form-select">
                    <option value="5">Last 5 matches</option>
                    <option value="10" selected>Last 10 matches</option>
                    <option value="15">Last 15 matches</option>
                    <option value="20">Last 20 matches</option>
                    <option value="25">Last 25 matches</option>
                    <option value="30">Last 30 matches</option>
                </select>
            </div>
            <div class="col-md-2">
                <button id="refreshData" class="btn btn-primary">Refresh</button>
            </div>
        </div>
        
        <div id="playersData">
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const matchId = {{ match_id }};
let currentData = null;

document.addEventListener('DOMContentLoaded', function() {
    loadMatchData();
    
    document.getElementById('statType').addEventListener('change', loadMatchData);
    document.getElementById('numMatches').addEventListener('change', loadMatchData);
    document.getElementById('refreshData').addEventListener('click', loadMatchData);
});

function loadMatchData() {
    const statType = document.getElementById('statType').value;
    const numMatches = document.getElementById('numMatches').value;
    
    document.getElementById('playersData').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';
    
    fetch(`/api/match/${matchId}/players?stat=${statType}&matches=${numMatches}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            currentData = data;
            displayMatchData(data);
        })
        .catch(error => {
            console.error('Error loading match data:', error);
            document.getElementById('playersData').innerHTML = '<div class="alert alert-danger">Error loading match data</div>';
        });
}

function displayMatchData(data) {
    const match = data.match;
    const team1Name = data.team1_name || 'Team 1';
    const team2Name = data.team2_name || 'Team 2';

    document.getElementById('matchTitle').textContent = `${team1Name} vs ${team2Name}`;

    const html = `
        <div class="team-section mb-5">
            <h3>${team1Name}</h3>
            ${generatePlayerTable(data.team1_players)}
        </div>

        <div class="team-section">
            <h3>${team2Name}</h3>
            ${generatePlayerTable(data.team2_players)}
        </div>
    `;

    document.getElementById('playersData').innerHTML = html;
}

function generatePlayerTable(players) {
    if (!players || players.length === 0) {
        return '<div class="alert alert-info">No player data available</div>';
    }
    
    const maxMatches = Math.max(...players.map(p => p.matches.length));
    
    let html = '<div class="player-stats-table">';
    html += '<div class="table-responsive">';
    html += '<table class="table table-bordered">';
    
    // Header
    html += '<thead><tr>';
    html += '<th>Player</th>';
    html += '<th>Avg/90</th>';
    for (let i = 0; i < maxMatches; i++) {
        html += `<th>Match ${i + 1}</th>`;
    }
    html += '</tr></thead>';
    
    // Body
    html += '<tbody>';
    players.forEach(player => {
        html += '<tr>';
        html += `<td class="player-name">${player.name}</td>`;
        html += `<td class="avg-stat">${player.stat_per_90.toFixed(2)}</td>`;
        
        for (let i = 0; i < maxMatches; i++) {
            if (i < player.matches.length) {
                const match = player.matches[i];
                html += generatePlayerCell(match);
            } else {
                html += '<td class="empty-cell">-</td>';
            }
        }
        html += '</tr>';
    });
    html += '</tbody>';
    html += '</table>';
    html += '</div>';
    html += '</div>';
    
    return html;
}

function generatePlayerCell(match) {
    const playedPercentage = Math.min((match.minutes / 90) * 100, 100);
    const statValue = match.stat_value;
    
    let cardsHtml = '';
    if (match.cards.yellow > 0) {
        cardsHtml += `<span class="card yellow-card">${match.cards.yellow}</span>`;
    }
    if (match.cards.red > 0) {
        cardsHtml += `<span class="card red-card">${match.cards.red}</span>`;
    }
    
    return `
        <td class="player-cell" style="background: linear-gradient(to right, #e3f2fd ${playedPercentage}%, transparent ${playedPercentage}%);">
            <div class="cell-content">
                <div class="minutes-played">${match.minutes}'</div>
                <div class="stat-value">${statValue}</div>
                <div class="cards">${cardsHtml}</div>
            </div>
        </td>
    `;
}
</script>
{% endblock %}