#!/usr/bin/env python3
"""
Debug the real data collection process
"""
import requests

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def debug_team_player_stats():
    """Debug the team player stats function step by step"""
    print("=== Debugging Team Player Stats Function ===")
    
    team_id = 6  # Tottenham from fixture 463
    stat_type = 'fouls_committed'
    num_matches = 5
    
    print(f"Looking for team_id: {team_id}")
    print(f"Stat type: {stat_type}")
    print(f"Number of matches: {num_matches}")
    
    # Step 1: Get fixtures
    print(f"\n1. Getting fixtures...")
    fixtures_data = make_api_request('fixtures', {'per_page': 10})
    
    if not fixtures_data or 'data' not in fixtures_data:
        print("❌ Could not get fixtures")
        return
    
    print(f"✅ Got {len(fixtures_data['data'])} fixtures")
    
    # Step 2: Find team fixtures
    print(f"\n2. Finding fixtures with team {team_id}...")
    team_fixtures = []
    
    for i, fixture in enumerate(fixtures_data['data'][:5]):  # Check first 5
        fixture_id = fixture['id']
        print(f"  Checking fixture {fixture_id}...")
        
        fixture_with_participants = make_api_request(f'fixtures/{fixture_id}', {'include': 'participants'})
        if fixture_with_participants and 'data' in fixture_with_participants:
            participants = fixture_with_participants['data'].get('participants', [])
            participant_names = [f"{p.get('name')} (ID: {p.get('id')})" for p in participants]
            print(f"    Participants: {participant_names}")
            
            if any(p.get('id') == team_id for p in participants):
                team_fixtures.append(fixture_id)
                print(f"    ✅ Team {team_id} found in fixture {fixture_id}")
            else:
                print(f"    - Team {team_id} not in this fixture")
        else:
            print(f"    ❌ Could not get participants for fixture {fixture_id}")
    
    print(f"\nFound {len(team_fixtures)} fixtures with team {team_id}: {team_fixtures}")
    
    if not team_fixtures:
        print("❌ No fixtures found for this team")
        return
    
    # Step 3: Get events for first fixture
    fixture_id = team_fixtures[0]
    print(f"\n3. Getting events for fixture {fixture_id}...")
    
    fixture_with_events = make_api_request(f'fixtures/{fixture_id}', {'include': 'events'})
    
    if not fixture_with_events or 'data' not in fixture_with_events:
        print("❌ Could not get events")
        return
    
    events = fixture_with_events['data'].get('events', [])
    print(f"✅ Got {len(events)} events")
    
    # Show events with our target types
    target_event_types = [19]  # Yellow cards for fouls_committed
    relevant_events = [e for e in events if e.get('type_id') in target_event_types]
    
    print(f"Events with type_id in {target_event_types}: {len(relevant_events)}")
    for event in relevant_events:
        print(f"  {event.get('player_name')} (ID: {event.get('player_id')}) - type_id: {event.get('type_id')}")
    
    # Step 4: Get lineups
    print(f"\n4. Getting lineups for fixture {fixture_id}...")
    
    fixture_with_lineups = make_api_request(f'fixtures/{fixture_id}', {'include': 'lineups'})
    
    if not fixture_with_lineups or 'data' not in fixture_with_lineups:
        print("❌ Could not get lineups")
        return
    
    lineups = fixture_with_lineups['data'].get('lineups', [])
    team_lineups = [l for l in lineups if l.get('team_id') == team_id]
    
    print(f"✅ Got {len(lineups)} total lineups, {len(team_lineups)} for team {team_id}")
    
    for lineup in team_lineups[:5]:  # Show first 5
        print(f"  {lineup.get('player_name')} (ID: {lineup.get('player_id')})")
    
    # Step 5: Match events to lineups
    print(f"\n5. Matching events to team lineups...")
    
    matched_events = []
    for event in relevant_events:
        event_player_id = event.get('player_id')
        matching_lineup = next((l for l in team_lineups if l.get('player_id') == event_player_id), None)
        
        if matching_lineup:
            matched_events.append(event)
            print(f"  ✅ Matched: {event.get('player_name')} - {event.get('type_id')}")
        else:
            print(f"  - No lineup match for {event.get('player_name')} (ID: {event_player_id})")
    
    print(f"\nTotal matched events: {len(matched_events)}")

def main():
    """Debug real data collection"""
    debug_team_player_stats()

if __name__ == "__main__":
    main()
