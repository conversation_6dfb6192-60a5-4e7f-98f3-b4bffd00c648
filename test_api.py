#!/usr/bin/env python3
"""
Test script to verify SportsMonk API integration and data structure
"""
import requests
import json
from datetime import datetime, timedelta

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def test_basic_connection():
    """Test basic API connection"""
    print("=== Testing Basic API Connection ===")
    data = make_api_request('fixtures', {'per_page': 1})
    if data:
        print("✓ API connection successful!")
        print(f"Response keys: {list(data.keys())}")
        if 'data' in data and len(data['data']) > 0:
            print(f"Sample fixture keys: {list(data['data'][0].keys())}")
        return True
    else:
        print("✗ API connection failed!")
        return False

def test_upcoming_matches():
    """Test upcoming matches endpoint"""
    print("\n=== Testing Upcoming Matches ===")

    # First, let's try to get any fixtures to understand the structure
    print("Trying basic fixtures request...")
    data = make_api_request('fixtures', {'per_page': 3})
    if data and 'data' in data and len(data['data']) > 0:
        print(f"✓ Found {len(data['data'])} fixtures")
        match = data['data'][0]
        print(f"Sample match structure:")
        print(f"  - ID: {match.get('id')}")
        print(f"  - Starting at: {match.get('starting_at')}")
        print(f"  - All keys: {list(match.keys())}")

        # Try with includes
        print("\nTrying with includes...")
        data_with_includes = make_api_request('fixtures', {
            'per_page': 1,
            'include': 'league,localTeam,visitorTeam'
        })
        if data_with_includes and 'data' in data_with_includes and len(data_with_includes['data']) > 0:
            match_inc = data_with_includes['data'][0]
            print(f"✓ Includes working. Keys: {list(match_inc.keys())}")
            if 'league' in match_inc:
                print(f"  - League structure: {type(match_inc['league'])}")
            if 'localTeam' in match_inc:
                print(f"  - LocalTeam structure: {type(match_inc['localTeam'])}")

        return match['id']
    else:
        print("✗ No fixtures found or API error")
        return None

def test_match_details(match_id):
    """Test match details endpoint"""
    print(f"\n=== Testing Match Details (ID: {match_id}) ===")
    data = make_api_request(f'fixtures/{match_id}', {
        'include': 'localTeam,visitorTeam,league'
    })
    
    if data and 'data' in data:
        match = data['data']
        print("✓ Match details retrieved successfully")
        print(f"  - Local Team ID: {match.get('localteam_id')}")
        print(f"  - Visitor Team ID: {match.get('visitorteam_id')}")
        return match.get('localteam_id'), match.get('visitorteam_id')
    else:
        print("✗ Failed to retrieve match details")
        return None, None

def test_team_fixtures(team_id):
    """Test team fixtures endpoint"""
    print(f"\n=== Testing Team Fixtures (Team ID: {team_id}) ===")
    data = make_api_request(f'teams/{team_id}/fixtures', {
        'include': 'stats.player',
        'per_page': 5
    })
    
    if data and 'data' in data:
        print(f"✓ Found {len(data['data'])} team fixtures")
        if len(data['data']) > 0:
            fixture = data['data'][0]
            print(f"Sample fixture keys: {list(fixture.keys())}")
            if 'stats' in fixture and fixture['stats']:
                print(f"Stats available: {len(fixture['stats']['data'])} player stats")
                if len(fixture['stats']['data']) > 0:
                    stat = fixture['stats']['data'][0]
                    print(f"Sample stat keys: {list(stat.keys())}")
                    if 'player' in stat and stat['player']:
                        player = stat['player']['data']
                        print(f"Sample player keys: {list(player.keys())}")
        return True
    else:
        print("✗ Failed to retrieve team fixtures")
        return False

def test_team_squad(team_id):
    """Test team squad endpoint"""
    print(f"\n=== Testing Team Squad (Team ID: {team_id}) ===")
    data = make_api_request(f'teams/{team_id}/squad', {
        'include': 'player'
    })

    if data and 'data' in data:
        print(f"✓ Found {len(data['data'])} squad players")
        if len(data['data']) > 0:
            player = data['data'][0]
            print(f"Sample squad player keys: {list(player.keys())}")
            if 'player' in player and player['player']:
                player_data = player['player']['data']
                print(f"Sample player data keys: {list(player_data.keys())}")
        return True
    else:
        print("✗ Failed to retrieve team squad")
        return False

def explore_api_structure():
    """Explore the API structure to understand available endpoints"""
    print("\n=== Exploring API Structure ===")

    # Test different endpoints to understand the API
    endpoints_to_test = [
        ('leagues', {'per_page': 1}),
        ('teams', {'per_page': 1}),
        ('players', {'per_page': 1}),
    ]

    for endpoint, params in endpoints_to_test:
        print(f"\nTesting {endpoint} endpoint...")
        data = make_api_request(endpoint, params)
        if data and 'data' in data and len(data['data']) > 0:
            print(f"✓ {endpoint} endpoint works")
            print(f"  Sample keys: {list(data['data'][0].keys())}")
        else:
            print(f"✗ {endpoint} endpoint failed or no data")

def test_fixture_includes():
    """Test what includes are available for fixtures"""
    print("\n=== Testing Fixture Includes ===")

    # Get a fixture first
    data = make_api_request('fixtures', {'per_page': 1})
    if not data or 'data' not in data or len(data['data']) == 0:
        print("✗ No fixtures available for testing")
        return None

    fixture_id = data['data'][0]['id']
    print(f"Testing with fixture ID: {fixture_id}")

    # Test different include combinations
    include_tests = [
        'league',
        'participants',
        'scores',
        'statistics',
        'lineups',
        'participants.image'
    ]

    for include in include_tests:
        print(f"\nTrying include: {include}")
        test_data = make_api_request(f'fixtures/{fixture_id}', {'include': include})
        if test_data and 'data' in test_data:
            fixture = test_data['data']
            print(f"✓ Include '{include}' works")
            print(f"  Keys: {list(fixture.keys())}")
            if include in fixture:
                print(f"  {include} type: {type(fixture[include])}")
        else:
            print(f"✗ Include '{include}' failed")

    return fixture_id

def main():
    """Run all API tests"""
    print("SportsMonk API Integration Test")
    print("=" * 40)

    # Test basic connection
    if not test_basic_connection():
        return

    # Explore API structure first
    explore_api_structure()

    # Test fixture includes to understand the correct structure
    match_id = test_fixture_includes()

    # Test upcoming matches
    if not match_id:
        match_id = test_upcoming_matches()

    if not match_id:
        print("\nNo matches found. Using first available fixture...")
        # Get any fixture ID from the basic test
        data = make_api_request('fixtures', {'per_page': 1})
        if data and 'data' in data and len(data['data']) > 0:
            match_id = data['data'][0]['id']
            print(f"Using fixture ID: {match_id}")

    if match_id:
        # Test match details
        local_team_id, visitor_team_id = test_match_details(match_id)

        if local_team_id:
            # Test team fixtures and squad
            test_team_fixtures(local_team_id)
            test_team_squad(local_team_id)

    print("\n" + "=" * 40)
    print("API testing complete!")

if __name__ == "__main__":
    main()
