#!/usr/bin/env python3
"""
Check for recent seasons and fixtures
"""
import requests
from datetime import datetime, timedelta

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def check_seasons():
    """Check available seasons"""
    print("=== Checking Available Seasons ===")
    
    seasons_data = make_api_request('seasons', {'per_page': 100})
    
    if not seasons_data or 'data' not in seasons_data:
        print("❌ Could not get seasons data")
        return
    
    seasons = seasons_data['data']
    print(f"Total seasons available: {len(seasons)}")
    
    # Look for recent seasons
    recent_seasons = []
    for season in seasons:
        name = season.get('name', '')
        if any(year in name for year in ['2024', '2023', '2022', '2021', '2020']):
            recent_seasons.append(season)
    
    print(f"\nRecent seasons found: {len(recent_seasons)}")
    for season in recent_seasons[:10]:  # Show first 10
        print(f"  - {season.get('name')} (ID: {season.get('id')})")
    
    return recent_seasons

def check_recent_fixtures():
    """Check for recent fixtures"""
    print("\n=== Checking Recent Fixtures ===")
    
    # Try different date ranges
    today = datetime.now()
    
    # Check last 30 days
    start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')
    
    print(f"Checking fixtures from {start_date} to {end_date}")
    
    fixtures_data = make_api_request('fixtures', {
        'filter[starting_at]': f'{start_date},{end_date}',
        'per_page': 50
    })
    
    if fixtures_data and 'data' in fixtures_data:
        fixtures = fixtures_data['data']
        print(f"Recent fixtures found: {len(fixtures)}")
        
        for fixture in fixtures[:5]:  # Show first 5
            date = fixture.get('starting_at', '')
            print(f"  - Fixture {fixture.get('id')}: {date}")
    else:
        print("No recent fixtures found")
    
    # Try checking fixtures without date filter
    print("\nChecking all available fixtures (no date filter):")
    all_fixtures = make_api_request('fixtures', {'per_page': 20})
    
    if all_fixtures and 'data' in all_fixtures:
        fixtures = all_fixtures['data']
        print(f"Total fixtures available: {len(fixtures)}")
        
        # Show date range
        dates = [f.get('starting_at', '') for f in fixtures if f.get('starting_at')]
        if dates:
            dates.sort()
            print(f"Date range: {dates[0]} to {dates[-1]}")
            
            # Show some examples
            print("\nExample fixtures:")
            for fixture in fixtures[:5]:
                date = fixture.get('starting_at', '')
                print(f"  - Fixture {fixture.get('id')}: {date}")

def check_leagues():
    """Check available leagues"""
    print("\n=== Checking Available Leagues ===")
    
    leagues_data = make_api_request('leagues', {'per_page': 50})
    
    if not leagues_data or 'data' not in leagues_data:
        print("❌ Could not get leagues data")
        return
    
    leagues = leagues_data['data']
    print(f"Total leagues available: {len(leagues)}")
    
    # Look for major leagues
    major_leagues = []
    for league in leagues:
        name = league.get('name', '').lower()
        if any(keyword in name for keyword in ['premier', 'champions', 'bundesliga', 'serie', 'la liga', 'ligue']):
            major_leagues.append(league)
    
    print(f"\nMajor leagues found: {len(major_leagues)}")
    for league in major_leagues[:10]:
        print(f"  - {league.get('name')} (ID: {league.get('id')})")

def main():
    """Check for recent data availability"""
    print("CHECKING FOR 2024/2025 SEASON DATA")
    print("=" * 50)
    
    recent_seasons = check_seasons()
    check_recent_fixtures()
    check_leagues()
    
    print("\n" + "=" * 50)
    if recent_seasons:
        print("✅ Recent seasons found! Checking if they have fixture data...")
        
        # Try to get fixtures for a recent season
        for season in recent_seasons[:3]:
            season_id = season.get('id')
            print(f"\nChecking fixtures for season: {season.get('name')}")
            
            season_fixtures = make_api_request('fixtures', {
                'filter[season_id]': season_id,
                'per_page': 10
            })
            
            if season_fixtures and 'data' in season_fixtures:
                fixtures = season_fixtures['data']
                print(f"  Fixtures found: {len(fixtures)}")
                if fixtures:
                    print(f"  Example: Fixture {fixtures[0].get('id')} - {fixtures[0].get('starting_at')}")
            else:
                print(f"  No fixtures found for season {season_id}")
    else:
        print("❌ No recent seasons found in free tier")
        print("💡 The free API tier appears to only have historical data (2008-2013)")

if __name__ == "__main__":
    main()
