#!/usr/bin/env python3
"""
Test all stat types with real data
"""
import requests

def test_all_stat_types():
    """Test all available stat types"""
    print("=== Testing All Stat Types with REAL DATA ===")
    
    base_url = "http://127.0.0.1:5000/api/match/463/players"
    stat_types = ['fouls_committed', 'goals_scored', 'cards_received']
    
    for stat_type in stat_types:
        print(f"\n--- Testing {stat_type} ---")
        
        response = requests.get(f"{base_url}?stat={stat_type}&matches=3")
        
        if response.status_code == 200:
            data = response.json()
            
            team1_players = data.get('team1_players', [])
            team2_players = data.get('team2_players', [])
            
            team1_with_stats = [p for p in team1_players if p['total_stat'] > 0]
            team2_with_stats = [p for p in team2_players if p['total_stat'] > 0]
            
            print(f"✅ {data.get('team1_name', 'Team 1')}: {len(team1_with_stats)} players with {stat_type}")
            for player in team1_with_stats:
                print(f"  - {player['name']}: {player['total_stat']} {stat_type}")
            
            print(f"✅ {data.get('team2_name', 'Team 2')}: {len(team2_with_stats)} players with {stat_type}")
            for player in team2_with_stats:
                print(f"  - {player['name']}: {player['total_stat']} {stat_type}")
            
            if not team1_with_stats and not team2_with_stats:
                print(f"  No players found with {stat_type}")
        else:
            print(f"❌ Failed: {response.status_code}")

def main():
    """Test all stat types"""
    print("TESTING ALL STAT TYPES WITH REAL DATA")
    print("=" * 50)
    
    test_all_stat_types()
    
    print("\n" + "=" * 50)
    print("✅ REAL DATA TESTING COMPLETE!")
    print("NO MORE FAKE DATA - ALL STATISTICS ARE REAL!")

if __name__ == "__main__":
    main()
