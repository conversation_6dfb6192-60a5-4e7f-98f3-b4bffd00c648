#!/usr/bin/env python3
"""
Check which team the event players belong to
"""
import requests

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def check_player_teams():
    """Check which team the event players belong to"""
    print("=== Checking Player Teams ===")
    
    fixture_id = 463
    
    # Get fixture data separately (combined includes fail)
    events_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'events'})
    lineups_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'lineups'})
    participants_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'participants'})

    if not all([events_data, lineups_data, participants_data]):
        print("❌ Could not get fixture data")
        return

    events = events_data['data'].get('events', [])
    lineups = lineups_data['data'].get('lineups', [])
    participants = participants_data['data'].get('participants', [])
    
    print(f"Fixture {fixture_id}:")
    team_names = [f"{p.get('name')} (ID: {p.get('id')})" for p in participants]
    print(f"  Teams: {team_names}")
    print(f"  Events: {len(events)}")
    print(f"  Lineups: {len(lineups)}")
    
    # Group lineups by team
    lineups_by_team = {}
    for lineup in lineups:
        team_id = lineup.get('team_id')
        if team_id not in lineups_by_team:
            lineups_by_team[team_id] = []
        lineups_by_team[team_id].append(lineup)
    
    print(f"\nLineups by team:")
    for team_id, team_lineups in lineups_by_team.items():
        team_name = next((p.get('name') for p in participants if p.get('id') == team_id), f'Team {team_id}')
        print(f"  {team_name} (ID: {team_id}): {len(team_lineups)} players")
    
    # Check each event player
    print(f"\nEvent players and their teams:")
    for event in events:
        player_id = event.get('player_id')
        player_name = event.get('player_name')
        event_type = event.get('type_id')
        
        if not player_id or not player_name:
            continue
        
        # Find which team this player belongs to
        player_lineup = next((l for l in lineups if l.get('player_id') == player_id), None)
        
        if player_lineup:
            team_id = player_lineup.get('team_id')
            team_name = next((p.get('name') for p in participants if p.get('id') == team_id), f'Team {team_id}')
            print(f"  {player_name} (ID: {player_id}) - {team_name} (ID: {team_id}) - Event type: {event_type}")
        else:
            print(f"  {player_name} (ID: {player_id}) - NO LINEUP FOUND - Event type: {event_type}")

def main():
    """Check player teams"""
    check_player_teams()

if __name__ == "__main__":
    main()
