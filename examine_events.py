#!/usr/bin/env python3
"""
Examine events data for real player statistics
"""
import requests
import json

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def examine_events_data():
    """Examine events data for player statistics"""
    print("=== Examining Events Data ===")
    
    # Test multiple fixtures to find ones with events
    fixtures_to_test = [463, 464, 465, 466, 467, 468, 469, 470]
    
    all_event_types = set()
    player_events = []
    
    for fixture_id in fixtures_to_test:
        print(f"\nFixture {fixture_id}:")
        
        # Get events for this fixture
        fixture_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'events'})
        
        if fixture_data and 'data' in fixture_data:
            events = fixture_data['data'].get('events', [])
            print(f"  Found {len(events)} events")
            
            for event in events:
                event_type = event.get('type_id')
                all_event_types.add(event_type)
                
                player_name = event.get('player_name')
                if player_name:
                    player_events.append({
                        'fixture_id': fixture_id,
                        'player_name': player_name,
                        'player_id': event.get('player_id'),
                        'type_id': event_type,
                        'minute': event.get('minute'),
                        'result': event.get('result'),
                        'info': event.get('info'),
                        'section': event.get('section')
                    })
                    
                    print(f"    {player_name}: type_id={event_type}, minute={event.get('minute')}, result={event.get('result')}")
    
    print(f"\n=== Summary ===")
    print(f"All event type_ids found: {sorted(all_event_types)}")
    print(f"Total player events: {len(player_events)}")
    
    # Group events by type
    events_by_type = {}
    for event in player_events:
        type_id = event['type_id']
        if type_id not in events_by_type:
            events_by_type[type_id] = []
        events_by_type[type_id].append(event)
    
    print(f"\nEvents by type:")
    for type_id, events in events_by_type.items():
        print(f"  Type {type_id}: {len(events)} events")
        # Show sample
        if events:
            sample = events[0]
            print(f"    Sample: {sample['player_name']} - {sample.get('result', 'N/A')}")

def analyze_event_types():
    """Try to understand what each event type means"""
    print("\n=== Analyzing Event Types ===")
    
    # Get events from multiple fixtures
    fixture_data = make_api_request('fixtures/463', {'include': 'events'})
    
    if fixture_data and 'data' in fixture_data:
        events = fixture_data['data'].get('events', [])
        
        print(f"Detailed analysis of {len(events)} events:")
        
        for event in events:
            print(f"\nEvent:")
            for key, value in event.items():
                print(f"  {key}: {value}")

def find_statistical_events():
    """Find events that could be used for statistics"""
    print("\n=== Finding Statistical Events ===")
    
    # Test multiple fixtures
    fixtures_to_test = [463, 464, 465, 466, 467, 468]
    
    stat_events = {
        'goals': [],
        'cards': [],
        'substitutions': [],
        'other': []
    }
    
    for fixture_id in fixtures_to_test:
        fixture_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'events'})
        
        if fixture_data and 'data' in fixture_data:
            events = fixture_data['data'].get('events', [])
            
            for event in events:
                player_name = event.get('player_name')
                if not player_name:
                    continue
                
                type_id = event.get('type_id')
                result = event.get('result', '').lower()
                info = event.get('info', '').lower()
                
                # Categorize events
                if 'goal' in result or 'goal' in info or type_id in [14, 15, 16]:  # Common goal type IDs
                    stat_events['goals'].append(event)
                elif 'card' in result or 'card' in info or type_id in [17, 18]:  # Common card type IDs
                    stat_events['cards'].append(event)
                elif 'substitution' in result or 'substitution' in info or type_id in [19, 20]:
                    stat_events['substitutions'].append(event)
                else:
                    stat_events['other'].append(event)
    
    print(f"Statistical events found:")
    for category, events in stat_events.items():
        print(f"  {category}: {len(events)} events")
        if events:
            sample = events[0]
            print(f"    Sample: {sample.get('player_name')} - type_id={sample.get('type_id')}, result={sample.get('result')}")

def main():
    """Examine events for real player statistics"""
    print("EXAMINING REAL PLAYER EVENTS DATA")
    print("=" * 50)
    
    examine_events_data()
    analyze_event_types()
    find_statistical_events()
    
    print("\n" + "=" * 50)
    print("Real events data analysis complete!")

if __name__ == "__main__":
    main()
