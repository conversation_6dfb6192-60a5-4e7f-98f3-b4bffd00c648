.match-card {
    cursor: pointer;
    transition: transform 0.2s;
}

.match-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.match-teams {
    text-align: center;
    margin: 10px 0;
}

.team {
    font-weight: bold;
    margin: 5px 0;
}

.vs {
    color: #666;
    font-size: 0.9em;
}

.player-stats-table {
    overflow-x: auto;
}

.player-cell {
    position: relative;
    min-width: 80px;
    height: 60px;
    padding: 4px !important;
    vertical-align: middle;
}

.cell-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.minutes-played {
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 0.7em;
    font-weight: bold;
    color: #333;
}

.stat-value {
    text-align: center;
    font-size: 1.1em;
    font-weight: bold;
    margin-top: 8px;
}

.cards {
    position: absolute;
    bottom: 2px;
    left: 4px;
    display: flex;
    gap: 2px;
}

.card {
    display: inline-block;
    width: 12px;
    height: 16px;
    font-size: 0.6em;
    text-align: center;
    line-height: 16px;
    border-radius: 2px;
    color: white;
    font-weight: bold;
}

.yellow-card {
    background-color: #ffc107;
    color: black;
}

.red-card {
    background-color: #dc3545;
}

.player-name {
    font-weight: bold;
    min-width: 150px;
}

.avg-stat {
    font-weight: bold;
    color: #0066cc;
}

.empty-cell {
    text-align: center;
    color: #ccc;
}

.team-section {
    margin-bottom: 40px;
}

.team-section h3 {
    color: #333;
    border-bottom: 2px solid #0066cc;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
}

.table td {
    text-align: center;
}