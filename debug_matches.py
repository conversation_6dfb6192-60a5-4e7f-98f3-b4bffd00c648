#!/usr/bin/env python3
"""
Debug why no matches are found
"""
import requests
from datetime import datetime, timedelta

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def debug_matches():
    """Debug match availability"""
    print("=== Debugging Match Availability ===")
    
    # Get fixtures without any filtering
    data = make_api_request('fixtures', {'per_page': 20})
    
    if not data or 'data' not in data:
        print("❌ Could not get fixtures")
        return
    
    print(f"Found {len(data['data'])} total fixtures")
    
    now = datetime.now()
    tomorrow = now + timedelta(days=1)
    next_week = now + timedelta(days=7)
    
    print(f"Current time: {now}")
    print(f"Looking for matches between {now} and {tomorrow}")
    
    upcoming_24h = []
    upcoming_week = []
    past_matches = []
    future_matches = []
    
    for match in data['data']:
        match_id = match['id']
        match_date_str = match.get('starting_at', 'Unknown')
        
        print(f"\nMatch {match_id}: {match_date_str}")
        
        try:
            match_time = datetime.strptime(match_date_str, '%Y-%m-%d %H:%M:%S')
            
            if match_time < now:
                past_matches.append((match_id, match_date_str))
                print(f"  → Past match")
            elif now <= match_time <= tomorrow:
                upcoming_24h.append((match_id, match_date_str))
                print(f"  → Upcoming (24h)")
            elif now <= match_time <= next_week:
                upcoming_week.append((match_id, match_date_str))
                print(f"  → Upcoming (week)")
            else:
                future_matches.append((match_id, match_date_str))
                print(f"  → Future match")
                
        except (ValueError, TypeError) as e:
            print(f"  → Date parsing error: {e}")
    
    print(f"\n=== Summary ===")
    print(f"Past matches: {len(past_matches)}")
    print(f"Upcoming (24h): {len(upcoming_24h)}")
    print(f"Upcoming (week): {len(upcoming_week)}")
    print(f"Future matches: {len(future_matches)}")
    
    # If no upcoming matches in 24h, let's try a different approach
    if len(upcoming_24h) == 0:
        print(f"\n=== No matches in next 24h, checking recent/future matches ===")
        
        # Show some recent matches for demonstration
        if past_matches:
            print(f"Recent matches available:")
            for match_id, date in past_matches[:5]:
                print(f"  {match_id}: {date}")
        
        if upcoming_week:
            print(f"Matches in next week:")
            for match_id, date in upcoming_week[:5]:
                print(f"  {match_id}: {date}")

def main():
    """Debug match availability"""
    debug_matches()

if __name__ == "__main__":
    main()
