#!/usr/bin/env python3
"""
Test what data is actually available in the SportsMonk API
"""
import requests
import json

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def test_available_data():
    """Test what data is available"""
    print("=== Testing Available Data ===")
    
    # Get several fixtures to test
    fixtures_data = make_api_request('fixtures', {'per_page': 10})
    
    if not fixtures_data or 'data' not in fixtures_data:
        print("✗ Could not get fixtures")
        return
    
    print(f"Testing {len(fixtures_data['data'])} fixtures for available data...")
    
    statistics_found = 0
    lineups_found = 0
    scores_found = 0
    
    for i, fixture in enumerate(fixtures_data['data']):
        fixture_id = fixture['id']
        print(f"\nFixture {i+1} (ID: {fixture_id}):")
        print(f"  Date: {fixture.get('starting_at', 'Unknown')}")
        
        # Test different includes
        includes_to_test = ['statistics', 'lineups', 'scores', 'participants']
        
        for include in includes_to_test:
            test_data = make_api_request(f'fixtures/{fixture_id}', {'include': include})
            if test_data and 'data' in test_data:
                fixture_data = test_data['data']
                if include in fixture_data and fixture_data[include]:
                    if include == 'statistics':
                        statistics_found += 1
                        print(f"  ✓ Statistics: {len(fixture_data[include])} entries")
                        if len(fixture_data[include]) > 0:
                            stat = fixture_data[include][0]
                            print(f"    Sample stat keys: {list(stat.keys())}")
                    elif include == 'lineups':
                        lineups_found += 1
                        print(f"  ✓ Lineups: {len(fixture_data[include])} entries")
                    elif include == 'scores':
                        scores_found += 1
                        print(f"  ✓ Scores: {len(fixture_data[include])} entries")
                    elif include == 'participants':
                        print(f"  ✓ Participants: {len(fixture_data[include])} teams")
                        for j, participant in enumerate(fixture_data[include]):
                            print(f"    Team {j+1}: {participant.get('name', 'Unknown')}")
                else:
                    print(f"  - No {include}")
            else:
                print(f"  ✗ Failed to get {include}")
    
    print(f"\n=== Summary ===")
    print(f"Fixtures with statistics: {statistics_found}/{len(fixtures_data['data'])}")
    print(f"Fixtures with lineups: {lineups_found}/{len(fixtures_data['data'])}")
    print(f"Fixtures with scores: {scores_found}/{len(fixtures_data['data'])}")

def test_alternative_approaches():
    """Test alternative approaches for getting player data"""
    print("\n=== Testing Alternative Approaches ===")
    
    # Test if we can get player data from other endpoints
    endpoints_to_test = [
        ('players', {'per_page': 3}),
        ('seasons', {'per_page': 3}),
        ('leagues', {'per_page': 3}),
    ]
    
    for endpoint, params in endpoints_to_test:
        print(f"\nTesting {endpoint} endpoint...")
        data = make_api_request(endpoint, params)
        if data and 'data' in data:
            print(f"  ✓ {endpoint} works - {len(data['data'])} items")
            if len(data['data']) > 0:
                print(f"    Sample keys: {list(data['data'][0].keys())}")
        else:
            print(f"  ✗ {endpoint} failed")

def main():
    """Run available data tests"""
    print("Testing Available Data in SportsMonk API")
    print("=" * 50)
    
    test_available_data()
    test_alternative_approaches()
    
    print("\n" + "=" * 50)
    print("Available data testing complete!")

if __name__ == "__main__":
    main()
