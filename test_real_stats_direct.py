#!/usr/bin/env python3
"""
Test real statistics directly without Flask
"""
import requests

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def get_real_player_stats_simple():
    """Get real player statistics for fixture 463"""
    print("=== Getting Real Player Statistics ===")
    
    fixture_id = 463
    
    # Get events
    events_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'events'})
    lineups_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'lineups'})
    participants_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'participants'})
    
    if not all([events_data, lineups_data, participants_data]):
        print("❌ Could not get fixture data")
        return
    
    events = events_data['data'].get('events', [])
    lineups = lineups_data['data'].get('lineups', [])
    participants = participants_data['data'].get('participants', [])
    
    print(f"Fixture {fixture_id}:")
    print(f"  Events: {len(events)}")
    print(f"  Lineups: {len(lineups)}")
    print(f"  Participants: {len(participants)}")
    
    # Group lineups by team
    lineups_by_team = {}
    for lineup in lineups:
        team_id = lineup.get('team_id')
        if team_id not in lineups_by_team:
            lineups_by_team[team_id] = []
        lineups_by_team[team_id].append(lineup)
    
    # Count events by team and type
    team_stats = {}
    
    for event in events:
        player_id = event.get('player_id')
        player_name = event.get('player_name')
        event_type = event.get('type_id')
        
        if not player_id or not player_name:
            continue
        
        # Find which team this player belongs to
        player_lineup = next((l for l in lineups if l.get('player_id') == player_id), None)
        
        if not player_lineup:
            continue
        
        team_id = player_lineup.get('team_id')
        team_name = next((p.get('name') for p in participants if p.get('id') == team_id), f'Team {team_id}')
        
        if team_id not in team_stats:
            team_stats[team_id] = {
                'team_name': team_name,
                'players': {}
            }
        
        if player_id not in team_stats[team_id]['players']:
            team_stats[team_id]['players'][player_id] = {
                'name': player_name,
                'goals': 0,
                'yellow_cards': 0,
                'red_cards': 0,
                'substitutions': 0
            }
        
        # Count events by type
        if event_type == 14 or event_type == 16:  # Goals
            team_stats[team_id]['players'][player_id]['goals'] += 1
        elif event_type == 19:  # Yellow cards
            team_stats[team_id]['players'][player_id]['yellow_cards'] += 1
        elif event_type == 17:  # Red cards
            team_stats[team_id]['players'][player_id]['red_cards'] += 1
        elif event_type == 18:  # Substitutions
            team_stats[team_id]['players'][player_id]['substitutions'] += 1
    
    # Display results
    print(f"\n=== REAL PLAYER STATISTICS ===")
    
    for team_id, team_data in team_stats.items():
        print(f"\n{team_data['team_name']} (ID: {team_id}):")
        
        players_with_stats = []
        for player_id, player_data in team_data['players'].items():
            total_events = (player_data['goals'] + player_data['yellow_cards'] + 
                          player_data['red_cards'] + player_data['substitutions'])
            if total_events > 0:
                players_with_stats.append((player_data['name'], player_data))
        
        if players_with_stats:
            for player_name, stats in players_with_stats:
                print(f"  {player_name}:")
                if stats['goals'] > 0:
                    print(f"    Goals: {stats['goals']}")
                if stats['yellow_cards'] > 0:
                    print(f"    Yellow cards: {stats['yellow_cards']}")
                if stats['red_cards'] > 0:
                    print(f"    Red cards: {stats['red_cards']}")
                if stats['substitutions'] > 0:
                    print(f"    Substitutions: {stats['substitutions']}")
        else:
            print("  No players with recorded events")
    
    return team_stats

def main():
    """Test real statistics"""
    print("TESTING REAL PLAYER STATISTICS")
    print("=" * 50)
    
    stats = get_real_player_stats_simple()
    
    if stats:
        print(f"\n✅ SUCCESS: Found real statistics for {len(stats)} teams")
        total_players_with_stats = sum(len([p for p in team['players'].values() 
                                          if any([p['goals'], p['yellow_cards'], p['red_cards'], p['substitutions']])]) 
                                     for team in stats.values())
        print(f"✅ Total players with statistics: {total_players_with_stats}")
    else:
        print("❌ FAILED: No statistics found")

if __name__ == "__main__":
    main()
