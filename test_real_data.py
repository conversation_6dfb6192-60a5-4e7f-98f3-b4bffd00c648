#!/usr/bin/env python3
"""
Test the real data implementation
"""
import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_real_player_stats():
    """Test the real player statistics endpoint"""
    print("=== Testing Real Player Statistics ===")
    
    # Test with a match we know has events (fixture 463)
    response = requests.get(f"{BASE_URL}/api/match/463/players?stat=fouls_committed&matches=5")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ API call successful")
        print(f"Response keys: {list(data.keys())}")
        
        if 'team1_players' in data and 'team2_players' in data:
            team1_players = data['team1_players']
            team2_players = data['team2_players']
            
            print(f"\nTeam 1 ({data.get('team1_name', 'Unknown')}): {len(team1_players)} players")
            for i, player in enumerate(team1_players[:5]):  # Show first 5
                print(f"  {i+1}. {player['name']}: {player['total_stat']} fouls, {player['stat_per_90']:.2f} per 90min")
                print(f"     Matches: {len(player['matches'])}, Total minutes: {player['total_minutes']}")
            
            print(f"\nTeam 2 ({data.get('team2_name', 'Unknown')}): {len(team2_players)} players")
            for i, player in enumerate(team2_players[:5]):  # Show first 5
                print(f"  {i+1}. {player['name']}: {player['total_stat']} fouls, {player['stat_per_90']:.2f} per 90min")
                print(f"     Matches: {len(player['matches'])}, Total minutes: {player['total_minutes']}")
        else:
            print("❌ Missing player data in response")
    else:
        print(f"❌ API call failed: {response.status_code}")
        print(response.text)

def test_different_stats():
    """Test different stat types"""
    print("\n=== Testing Different Stat Types ===")
    
    stat_types = ['fouls_committed', 'goals_scored', 'cards_received']
    
    for stat_type in stat_types:
        print(f"\nTesting {stat_type}:")
        response = requests.get(f"{BASE_URL}/api/match/463/players?stat={stat_type}&matches=3")
        
        if response.status_code == 200:
            data = response.json()
            team1_players = data.get('team1_players', [])
            team2_players = data.get('team2_players', [])
            
            # Count players with stats > 0
            team1_with_stats = [p for p in team1_players if p['total_stat'] > 0]
            team2_with_stats = [p for p in team2_players if p['total_stat'] > 0]
            
            print(f"  Team 1: {len(team1_with_stats)}/{len(team1_players)} players with {stat_type}")
            print(f"  Team 2: {len(team2_with_stats)}/{len(team2_players)} players with {stat_type}")
            
            # Show top performer from each team
            if team1_with_stats:
                top1 = team1_with_stats[0]
                print(f"  Top Team 1: {top1['name']} - {top1['total_stat']} {stat_type}")
            
            if team2_with_stats:
                top2 = team2_with_stats[0]
                print(f"  Top Team 2: {top2['name']} - {top2['total_stat']} {stat_type}")
        else:
            print(f"  ❌ Failed: {response.status_code}")

def main():
    """Test real data implementation"""
    print("TESTING REAL PLAYER STATISTICS DATA")
    print("=" * 50)
    
    test_real_player_stats()
    test_different_stats()
    
    print("\n" + "=" * 50)
    print("Real data testing complete!")

if __name__ == "__main__":
    main()
