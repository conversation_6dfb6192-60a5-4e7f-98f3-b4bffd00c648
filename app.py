from flask import Flask, render_template, request, jsonify
import requests
from datetime import datetime, timedelta
import json

app = Flask(__name__)

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

@app.route('/')
def index():
    """Main page showing upcoming matches"""
    return render_template('index.html')

@app.route('/api/test')
def test_api():
    """Test API connection"""
    data = make_api_request('fixtures', {'per_page': 1})
    return jsonify(data)

@app.route('/api/upcoming-matches')
def get_upcoming_matches():
    """Get upcoming matches in next 24 hours"""
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    today = datetime.now().strftime('%Y-%m-%d')
    
    params = {
        'filter': f'startingAt:{today},{tomorrow}',
        'include': 'league,localTeam,visitorTeam',
        'per_page': 50
    }
    
    data = make_api_request('fixtures', params)
    if data:
        print(f"API Response structure: {list(data.keys()) if data else 'None'}")
    return jsonify(data)

@app.route('/match/<int:match_id>')
def match_details(match_id):
    """Match details page"""
    return render_template('match_details.html', match_id=match_id)

@app.route('/api/match/<int:match_id>/players')
def get_match_players(match_id):
    """Get players and their stats for a specific match"""
    stat_type = request.args.get('stat', 'fouls_committed')
    num_matches = int(request.args.get('matches', 10))
    
    # Get match details
    match_data = make_api_request(f'fixtures/{match_id}', {
        'include': 'localTeam,visitorTeam,league'
    })
    
    if not match_data:
        return jsonify({'error': 'Match not found'})
    
    # Get team squads and player stats
    local_team_id = match_data['data']['localteam_id']
    visitor_team_id = match_data['data']['visitorteam_id']
    
    local_players = get_team_player_stats(local_team_id, stat_type, num_matches)
    visitor_players = get_team_player_stats(visitor_team_id, stat_type, num_matches)
    
    return jsonify({
        'match': match_data['data'],
        'local_team_players': local_players,
        'visitor_team_players': visitor_players
    })

def get_team_player_stats(team_id, stat_type, num_matches):
    """Get player stats for a team's last n matches"""
    # Get team's recent fixtures
    team_fixtures = make_api_request(f'teams/{team_id}/fixtures', {
        'include': 'stats.player',
        'per_page': num_matches
    })
    
    if not team_fixtures:
        return []
    
    # Get squad
    squad_data = make_api_request(f'teams/{team_id}/squad', {
        'include': 'player'
    })
    
    players_stats = {}
    
    # Process each fixture
    for fixture in team_fixtures['data'][:num_matches]:
        if 'stats' in fixture and fixture['stats']:
            for stat in fixture['stats']['data']:
                if 'player' in stat and stat['player']:
                    player_id = stat['player']['data']['player_id']
                    player_name = stat['player']['data']['display_name']
                    
                    if player_id not in players_stats:
                        players_stats[player_id] = {
                            'name': player_name,
                            'matches': [],
                            'total_stat': 0,
                            'total_minutes': 0
                        }
                    
                    match_stat = {
                        'date': fixture['time']['starting_at']['date'],
                        'minutes': stat.get('minutes', 0),
                        'stat_value': stat.get(stat_type, 0),
                        'cards': {
                            'yellow': stat.get('yellowcards', 0),
                            'red': stat.get('redcards', 0)
                        }
                    }
                    
                    players_stats[player_id]['matches'].append(match_stat)
                    players_stats[player_id]['total_stat'] += match_stat['stat_value']
                    players_stats[player_id]['total_minutes'] += match_stat['minutes']
    
    # Calculate averages and sort by likelihood
    for player_id in players_stats:
        player = players_stats[player_id]
        if player['total_minutes'] > 0:
            player['stat_per_90'] = (player['total_stat'] / player['total_minutes']) * 90
        else:
            player['stat_per_90'] = 0
    
    # Sort by stat_per_90 descending
    sorted_players = sorted(players_stats.values(), key=lambda x: x['stat_per_90'], reverse=True)
    
    return sorted_players

if __name__ == '__main__':
    # Test API connection first
    print("Testing API connection...")
    test_response = make_api_request('fixtures', {'per_page': 1})
    if test_response:
        print("API connection successful!")
        print(f"Response keys: {list(test_response.keys())}")
    else:
        print("API connection failed!")
    
    app.run(debug=True)
