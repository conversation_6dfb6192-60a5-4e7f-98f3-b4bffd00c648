"""
Football Match Analytics Flask Application

Note: This application uses the SportsMonk API. Due to API limitations in the free tier,
detailed player statistics (fouls, shots, etc.) are not available. The application
demonstrates the interface using mock statistical data based on real player lineups.

In a production environment with a premium API subscription, this would be replaced
with actual player performance data.
"""

from flask import Flask, render_template, request, jsonify
import requests

app = Flask(__name__)

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

@app.route('/')
def index():
    """Main page showing upcoming matches"""
    return render_template('index.html')

@app.route('/api/test')
def test_api():
    """Test API connection"""
    data = make_api_request('fixtures', {'per_page': 1})
    return jsonify(data)

@app.route('/api/upcoming-matches')
def get_upcoming_matches():
    """Get recent matches for demonstration (API contains historical data only)"""
    # Get basic fixtures first
    data = make_api_request('fixtures', {'per_page': 20})

    if not data or 'data' not in data:
        return jsonify({'error': 'Failed to fetch fixtures'})

    # Since the free API only has historical data, we'll show recent matches
    # and enrich them with details for demonstration purposes
    enriched_matches = []

    for match in data['data'][:10]:  # Limit to 10 matches for better performance
        try:
            # Get participants and league info separately
            match_with_participants = make_api_request(f'fixtures/{match["id"]}', {'include': 'participants'})
            match_with_league = make_api_request(f'fixtures/{match["id"]}', {'include': 'league'})

            if match_with_participants and 'data' in match_with_participants:
                match['participants'] = match_with_participants['data'].get('participants', [])

            if match_with_league and 'data' in match_with_league:
                match['league'] = match_with_league['data'].get('league', {})

            # Only add matches that have participants (teams)
            if 'participants' in match and len(match['participants']) >= 2:
                enriched_matches.append(match)

        except (ValueError, KeyError):
            continue

    # Update the data with enriched matches
    data['data'] = enriched_matches
    print(f"Found {len(enriched_matches)} matches for demonstration")

    return jsonify(data)

@app.route('/match/<int:match_id>')
def match_details(match_id):
    """Match details page"""
    return render_template('match_details.html', match_id=match_id)

@app.route('/api/match/<int:match_id>/players')
def get_match_players(match_id):
    """Get players and their stats for a specific match"""
    stat_type = request.args.get('stat', 'fouls_committed')
    num_matches = int(request.args.get('matches', 10))

    # Get match details with participants and league separately
    match_with_participants = make_api_request(f'fixtures/{match_id}', {'include': 'participants'})
    match_with_league = make_api_request(f'fixtures/{match_id}', {'include': 'league'})

    if not match_with_participants or 'data' not in match_with_participants:
        return jsonify({'error': 'Match not found'})

    match = match_with_participants['data']

    # Add league info if available
    if match_with_league and 'data' in match_with_league:
        match['league'] = match_with_league['data'].get('league', {})

    # Extract team IDs from participants
    if 'participants' not in match or len(match['participants']) < 2:
        return jsonify({'error': 'Match participants not found'})

    team1_id = match['participants'][0]['id']
    team2_id = match['participants'][1]['id']

    team1_players = get_team_player_stats(team1_id, stat_type, num_matches)
    team2_players = get_team_player_stats(team2_id, stat_type, num_matches)

    return jsonify({
        'match': match,
        'team1_players': team1_players,
        'team2_players': team2_players,
        'team1_name': match['participants'][0]['name'],
        'team2_name': match['participants'][1]['name']
    })

def get_team_player_stats(team_id, stat_type, num_matches):
    """Get player lineup data and generate mock statistics for demonstration"""
    import random

    # Since detailed player statistics are not available in this API tier,
    # we'll use lineup data and generate realistic mock statistics for demonstration

    # Get recent fixtures to find lineups
    fixtures_data = make_api_request('fixtures', {'per_page': 50})

    if not fixtures_data or 'data' not in fixtures_data:
        return []

    # Find fixtures involving this team
    team_fixtures = []
    for fixture in fixtures_data['data']:
        # Get participants for this fixture
        fixture_with_participants = make_api_request(f'fixtures/{fixture["id"]}', {'include': 'participants'})
        if fixture_with_participants and 'data' in fixture_with_participants:
            participants = fixture_with_participants['data'].get('participants', [])
            if any(p.get('id') == team_id for p in participants):
                team_fixtures.append(fixture['id'])
                if len(team_fixtures) >= num_matches:
                    break

    if not team_fixtures:
        return []

    # Get lineup data from the first available fixture
    fixture_with_lineups = make_api_request(f'fixtures/{team_fixtures[0]}', {'include': 'lineups'})

    if not fixture_with_lineups or 'data' not in fixture_with_lineups:
        return []

    lineups = fixture_with_lineups['data'].get('lineups', [])
    team_players = [lineup for lineup in lineups if lineup.get('team_id') == team_id]

    if not team_players:
        return []

    # Generate mock statistics for demonstration
    players_stats = []

    # Define realistic ranges for different stat types
    stat_ranges = {
        'fouls_committed': (0, 3),
        'fouls_drawn': (0, 2),
        'shots_total': (0, 4)
    }

    stat_range = stat_ranges.get(stat_type, (0, 2))

    for player in team_players[:15]:  # Limit to 15 players for display
        player_name = player.get('player_name', f'Player {player.get("player_id", "Unknown")}')

        # Generate mock match data
        matches = []
        total_stat = 0
        total_minutes = 0

        for _ in range(min(num_matches, 10)):  # Generate up to 10 matches
            minutes = random.randint(0, 90)  # Random minutes played
            stat_value = random.randint(stat_range[0], stat_range[1])
            yellow_cards = random.randint(0, 1) if random.random() < 0.15 else 0
            red_cards = random.randint(0, 1) if random.random() < 0.02 else 0

            match_stat = {
                'date': f'2024-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}',
                'minutes': minutes,
                'stat_value': stat_value,
                'cards': {
                    'yellow': yellow_cards,
                    'red': red_cards
                }
            }

            matches.append(match_stat)
            total_stat += stat_value
            total_minutes += minutes

        # Calculate per-90 average
        stat_per_90 = (total_stat / total_minutes * 90) if total_minutes > 0 else 0

        players_stats.append({
            'name': player_name,
            'matches': matches,
            'total_stat': total_stat,
            'total_minutes': total_minutes,
            'stat_per_90': stat_per_90
        })

    # Sort by stat_per_90 descending
    players_stats.sort(key=lambda x: x['stat_per_90'], reverse=True)

    return players_stats

if __name__ == '__main__':
    # Test API connection first
    print("Testing API connection...")
    test_response = make_api_request('fixtures', {'per_page': 1})
    if test_response:
        print("API connection successful!")
        print(f"Response keys: {list(test_response.keys())}")
    else:
        print("API connection failed!")
    
    app.run(debug=True)
