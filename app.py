"""
Football Match Analytics Flask Application

Note: This application uses the SportsMonk API. Due to API limitations in the free tier,
detailed player statistics (fouls, shots, etc.) are not available. The application
demonstrates the interface using mock statistical data based on real player lineups.

In a production environment with a premium API subscription, this would be replaced
with actual player performance data.
"""

from flask import Flask, render_template, request, jsonify
import requests

app = Flask(__name__)

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

@app.route('/')
def index():
    """Main page showing upcoming matches"""
    return render_template('index.html')

@app.route('/api/test')
def test_api():
    """Test API connection"""
    data = make_api_request('fixtures', {'per_page': 1})
    return jsonify(data)

@app.route('/api/upcoming-matches')
def get_upcoming_matches():
    """Get recent matches for demonstration (API contains historical data only)"""
    # Get basic fixtures first
    data = make_api_request('fixtures', {'per_page': 20})

    if not data or 'data' not in data:
        return jsonify({'error': 'Failed to fetch fixtures'})

    # Since the free API only has historical data, we'll show recent matches
    # and enrich them with details for demonstration purposes
    enriched_matches = []

    for match in data['data'][:10]:  # Limit to 10 matches for better performance
        try:
            # Get participants and league info separately
            match_with_participants = make_api_request(f'fixtures/{match["id"]}', {'include': 'participants'})
            match_with_league = make_api_request(f'fixtures/{match["id"]}', {'include': 'league'})

            if match_with_participants and 'data' in match_with_participants:
                match['participants'] = match_with_participants['data'].get('participants', [])

            if match_with_league and 'data' in match_with_league:
                match['league'] = match_with_league['data'].get('league', {})

            # Only add matches that have participants (teams)
            if 'participants' in match and len(match['participants']) >= 2:
                enriched_matches.append(match)

        except (ValueError, KeyError):
            continue

    # Update the data with enriched matches
    data['data'] = enriched_matches
    print(f"Found {len(enriched_matches)} matches for demonstration")

    return jsonify(data)

@app.route('/match/<int:match_id>')
def match_details(match_id):
    """Match details page"""
    return render_template('match_details.html', match_id=match_id)

@app.route('/api/match/<int:match_id>/players')
def get_match_players(match_id):
    """Get players and their stats for a specific match"""
    stat_type = request.args.get('stat', 'fouls_committed')
    num_matches = int(request.args.get('matches', 10))

    # Get match details with participants and league separately
    match_with_participants = make_api_request(f'fixtures/{match_id}', {'include': 'participants'})
    match_with_league = make_api_request(f'fixtures/{match_id}', {'include': 'league'})

    if not match_with_participants or 'data' not in match_with_participants:
        return jsonify({'error': 'Match not found'})

    match = match_with_participants['data']

    # Add league info if available
    if match_with_league and 'data' in match_with_league:
        match['league'] = match_with_league['data'].get('league', {})

    # Extract team IDs from participants
    if 'participants' not in match or len(match['participants']) < 2:
        return jsonify({'error': 'Match participants not found'})

    team1_id = match['participants'][0]['id']
    team2_id = match['participants'][1]['id']

    team1_players = get_team_player_stats(team1_id, stat_type, num_matches)
    team2_players = get_team_player_stats(team2_id, stat_type, num_matches)

    return jsonify({
        'match': match,
        'team1_players': team1_players,
        'team2_players': team2_players,
        'team1_name': match['participants'][0]['name'],
        'team2_name': match['participants'][1]['name']
    })

def get_team_player_stats(team_id, stat_type, num_matches):
    """Get REAL player statistics from match events data - SIMPLIFIED VERSION"""

    # For now, let's focus on the current match (fixture 463) since we know it has real data
    # TODO: Later expand to use num_matches parameter for multiple fixtures
    fixture_id = 463

    # Get fixture data separately (combined includes fail)
    events_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'events'})
    lineups_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'lineups'})
    participants_data = make_api_request(f'fixtures/{fixture_id}', {'include': 'participants'})

    if not all([events_data, lineups_data, participants_data]):
        return []

    events = events_data['data'].get('events', [])
    lineups = lineups_data['data'].get('lineups', [])
    participants = participants_data['data'].get('participants', [])

    # Check if this team is in this fixture
    if not any(p.get('id') == team_id for p in participants):
        return []

    # Map stat types to event types
    event_type_map = {
        'fouls_committed': [19],  # Yellow cards
        'goals_scored': [14, 16],  # Goals
        'cards_received': [17, 19]  # Red and yellow cards
    }

    target_event_types = event_type_map.get(stat_type, [19])

    # Collect player statistics
    players_stats = {}

    for event in events:
        player_id = event.get('player_id')
        player_name = event.get('player_name')
        event_type = event.get('type_id')

        if not player_id or not player_name or event_type not in target_event_types:
            continue

        # Check if this player is from our team
        player_lineup = next((l for l in lineups if l.get('player_id') == player_id and l.get('team_id') == team_id), None)
        if not player_lineup:
            continue

        # Initialize player stats if not exists
        if player_id not in players_stats:
            players_stats[player_id] = {
                'name': player_name,
                'matches': [{
                    'fixture_id': fixture_id,
                    'date': events_data['data'].get('starting_at', ''),
                    'minutes': 90,  # Assume full match for now
                    'stat_value': 0,
                    'cards': {'yellow': 0, 'red': 0}
                }],
                'total_stat': 0,
                'total_minutes': 90
            }

        match_entry = players_stats[player_id]['matches'][0]

        # Count the event
        if event_type == 14 or event_type == 16:  # Goals
            match_entry['stat_value'] += 1
            players_stats[player_id]['total_stat'] += 1
        elif event_type == 19:  # Yellow card
            match_entry['cards']['yellow'] += 1
            if stat_type == 'fouls_committed' or stat_type == 'cards_received':
                match_entry['stat_value'] += 1
                players_stats[player_id]['total_stat'] += 1
        elif event_type == 17:  # Red card
            match_entry['cards']['red'] += 1
            if stat_type == 'cards_received':
                match_entry['stat_value'] += 1
                players_stats[player_id]['total_stat'] += 1

    # Convert to list and calculate per-90 stats
    result = []
    for player_id, stats in players_stats.items():
        stats['stat_per_90'] = (stats['total_stat'] / stats['total_minutes']) * 90
        result.append(stats)

    # Sort by stat_per_90 descending
    result.sort(key=lambda x: x['stat_per_90'], reverse=True)

    return result

if __name__ == '__main__':
    # Test API connection first
    print("Testing API connection...")
    test_response = make_api_request('fixtures', {'per_page': 1})
    if test_response:
        print("API connection successful!")
        print(f"Response keys: {list(test_response.keys())}")
    else:
        print("API connection failed!")
    
    app.run(debug=True)
