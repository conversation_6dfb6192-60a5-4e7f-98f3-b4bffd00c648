{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>Upcoming Football Matches</h1>
        
        <div class="row mb-3">
            <div class="col-md-4">
                <select id="leagueFilter" class="form-select">
                    <option value="">All Leagues</option>
                </select>
            </div>
            <div class="col-md-2">
                <button id="refreshBtn" class="btn btn-primary">Refresh</button>
            </div>
        </div>
        
        <div id="matchesList" class="row">
            <div class="col-12 text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadUpcomingMatches();
    
    document.getElementById('refreshBtn').addEventListener('click', loadUpcomingMatches);
    document.getElementById('leagueFilter').addEventListener('change', filterMatches);
});

let allMatches = [];

function loadUpcomingMatches() {
    document.getElementById('matchesList').innerHTML = '<div class="col-12 text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';
    
    fetch('/api/upcoming-matches')
        .then(response => response.json())
        .then(data => {
            if (data && data.data) {
                allMatches = data.data;
                populateLeagueFilter();
                displayMatches(allMatches);
            }
        })
        .catch(error => {
            console.error('Error loading matches:', error);
            document.getElementById('matchesList').innerHTML = '<div class="col-12"><div class="alert alert-danger">Error loading matches</div></div>';
        });
}

function populateLeagueFilter() {
    const leagues = [...new Set(allMatches.map(match => match.league?.name || 'Unknown League'))];
    const select = document.getElementById('leagueFilter');

    select.innerHTML = '<option value="">All Leagues</option>';
    leagues.forEach(league => {
        const option = document.createElement('option');
        option.value = league;
        option.textContent = league;
        select.appendChild(option);
    });
}

function filterMatches() {
    const selectedLeague = document.getElementById('leagueFilter').value;
    const filteredMatches = selectedLeague ?
        allMatches.filter(match => (match.league?.name || 'Unknown League') === selectedLeague) :
        allMatches;

    displayMatches(filteredMatches);
}

function displayMatches(matches) {
    const container = document.getElementById('matchesList');
    
    if (matches.length === 0) {
        container.innerHTML = '<div class="col-12"><div class="alert alert-info">No matches found</div></div>';
        return;
    }
    
    const matchesHtml = matches.map(match => {
        const team1 = match.participants?.[0]?.name || 'Team 1';
        const team2 = match.participants?.[1]?.name || 'Team 2';
        const leagueName = match.league?.name || 'Unknown League';
        const matchTime = match.starting_at ? new Date(match.starting_at).toLocaleString() : 'TBD';

        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card match-card" onclick="selectMatch(${match.id})">
                    <div class="card-body">
                        <h6 class="card-title">${leagueName}</h6>
                        <div class="match-teams">
                            <div class="team">${team1}</div>
                            <div class="vs">vs</div>
                            <div class="team">${team2}</div>
                        </div>
                        <small class="text-muted">${matchTime}</small>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = matchesHtml;
}

function selectMatch(matchId) {
    window.location.href = `/match/${matchId}`;
}
</script>
{% endblock %}