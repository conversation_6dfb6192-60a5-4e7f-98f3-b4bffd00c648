#!/usr/bin/env python3
"""
Test Flask endpoint with debugging
"""
import requests
import json

def test_flask_endpoint():
    """Test the Flask endpoint with debugging"""
    print("=== Testing Flask Endpoint ===")
    
    url = "http://127.0.0.1:5000/api/match/463/players?stat=fouls_committed&matches=3"
    
    print(f"Calling: {url}")
    
    response = requests.get(url)
    
    print(f"Status code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        
        print(f"Response keys: {list(data.keys())}")
        
        if 'match' in data:
            match = data['match']
            print(f"Match ID: {match.get('id')}")
            print(f"Participants: {len(match.get('participants', []))}")
            
            for i, participant in enumerate(match.get('participants', [])):
                print(f"  Team {i+1}: {participant.get('name')} (ID: {participant.get('id')})")
        
        print(f"Team 1 players: {len(data.get('team1_players', []))}")
        print(f"Team 2 players: {len(data.get('team2_players', []))}")
        
        # Show any players with stats
        for team_num, team_key in enumerate(['team1_players', 'team2_players'], 1):
            players = data.get(team_key, [])
            players_with_stats = [p for p in players if p.get('total_stat', 0) > 0]
            
            print(f"Team {team_num} players with stats: {len(players_with_stats)}")
            for player in players_with_stats[:3]:  # Show first 3
                print(f"  {player['name']}: {player['total_stat']} fouls")
    else:
        print(f"Error: {response.text}")

def main():
    """Test Flask endpoint"""
    test_flask_endpoint()

if __name__ == "__main__":
    main()
