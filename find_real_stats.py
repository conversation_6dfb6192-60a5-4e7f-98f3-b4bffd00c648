#!/usr/bin/env python3
"""
Find real player statistics data in the API
"""
import requests
import json

# SportsMonk API configuration
API_TOKEN = "************************************************************"
BASE_URL = "https://api.sportmonks.com/v3/football"

def make_api_request(endpoint, params=None):
    """Make request to SportsMonk API"""
    if params is None:
        params = {}
    params['api_token'] = API_TOKEN
    
    try:
        response = requests.get(f"{BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}")
        return None

def explore_all_endpoints():
    """Explore all possible endpoints for real statistics"""
    print("=== Exploring All Possible Endpoints ===")
    
    # Test various endpoints that might have player stats
    endpoints_to_test = [
        'fixtures',
        'players',
        'teams',
        'leagues',
        'seasons',
        'standings',
        'topscorers',
        'cards',
        'corners',
        'goals',
        'substitutions',
        'lineups',
        'statistics',
        'events',
        'commentaries'
    ]
    
    working_endpoints = []
    
    for endpoint in endpoints_to_test:
        print(f"\nTesting {endpoint}...")
        data = make_api_request(endpoint, {'per_page': 2})
        if data and 'data' in data:
            print(f"  ✓ {endpoint} works - {len(data['data'])} items")
            working_endpoints.append(endpoint)
            
            if len(data['data']) > 0:
                sample = data['data'][0]
                print(f"    Sample keys: {list(sample.keys())}")
                
                # Look for any stat-related fields
                stat_fields = []
                for key, value in sample.items():
                    if any(stat_word in key.lower() for stat_word in ['stat', 'goal', 'card', 'foul', 'shot', 'assist']):
                        stat_fields.append(f"{key}: {value}")
                
                if stat_fields:
                    print(f"    Potential stat fields: {stat_fields}")
        else:
            print(f"  ✗ {endpoint} failed")
    
    return working_endpoints

def explore_player_specific_data():
    """Explore player-specific endpoints"""
    print("\n=== Exploring Player-Specific Data ===")
    
    # Get a player ID first
    players_data = make_api_request('players', {'per_page': 5})
    if not players_data or 'data' not in players_data:
        print("Cannot get players")
        return
    
    player_id = players_data['data'][0]['id']
    player_name = players_data['data'][0]['name']
    print(f"Testing with player: {player_name} (ID: {player_id})")
    
    # Test player-specific endpoints
    player_endpoints = [
        f'players/{player_id}',
        f'players/{player_id}/statistics',
        f'players/{player_id}/fixtures',
        f'players/{player_id}/transfers',
        f'players/{player_id}/trophies'
    ]
    
    for endpoint in player_endpoints:
        print(f"\nTesting {endpoint}...")
        data = make_api_request(endpoint)
        if data:
            print(f"  ✓ Works")
            if 'data' in data:
                if isinstance(data['data'], list):
                    print(f"    {len(data['data'])} items")
                    if len(data['data']) > 0:
                        print(f"    Sample: {data['data'][0]}")
                else:
                    print(f"    Single item: {list(data['data'].keys())}")
        else:
            print(f"  ✗ Failed")

def explore_fixture_includes():
    """Explore all possible fixture includes"""
    print("\n=== Exploring All Fixture Includes ===")
    
    fixture_id = 463  # We know this exists
    
    # Test all possible includes one by one
    possible_includes = [
        'league', 'participants', 'lineups', 'scores', 'statistics', 
        'events', 'corners', 'cards', 'goals', 'substitutions',
        'bookmakers', 'periods', 'venue', 'referees', 'weather',
        'commentaries', 'highlights', 'round', 'stage', 'group'
    ]
    
    working_includes = []
    
    for include in possible_includes:
        print(f"\nTesting fixture/{fixture_id}?include={include}")
        data = make_api_request(f'fixtures/{fixture_id}', {'include': include})
        
        if data and 'data' in data:
            fixture = data['data']
            if include in fixture and fixture[include]:
                print(f"  ✓ {include} works - {len(fixture[include])} items")
                working_includes.append(include)
                
                # Show sample data
                sample = fixture[include][0] if isinstance(fixture[include], list) else fixture[include]
                print(f"    Sample keys: {list(sample.keys()) if isinstance(sample, dict) else type(sample)}")
                
                # Look for statistical data
                if isinstance(sample, dict):
                    for key, value in sample.items():
                        if any(stat_word in key.lower() for stat_word in ['stat', 'goal', 'card', 'foul', 'shot', 'minute', 'time']):
                            print(f"      {key}: {value}")
            else:
                print(f"  - {include} empty")
        else:
            print(f"  ✗ {include} failed")
    
    print(f"\nWorking includes: {working_includes}")
    return working_includes

def main():
    """Find real statistics data"""
    print("FINDING REAL PLAYER STATISTICS DATA")
    print("=" * 50)
    
    working_endpoints = explore_all_endpoints()
    explore_player_specific_data()
    working_includes = explore_fixture_includes()
    
    print("\n" + "=" * 50)
    print("SUMMARY OF REAL DATA AVAILABLE:")
    print(f"Working endpoints: {working_endpoints}")
    print(f"Working fixture includes: {working_includes}")

if __name__ == "__main__":
    main()
